{% load static %}
<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>明星生日需求上传</title>
    <style>
        .container {
            max-width: 90%;
            margin-top: 15px;
            background: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
            margin-right: auto;
            margin-left: auto;
            position: relative;
        }

        h1, h2, h3 {
            font-weight: 600;
            color: #333;
        }

        .section-title {
            margin-bottom: 15px;
            color: #4a4a4a;
            font-size: 1.2rem;
        }

        .file-upload-container {
            margin-top: 10px;
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            background-color: #f9f9f9;
            transition: all 0.3s ease;
        }

        .file-upload-container.dragover {
            border-color: #4CAF50;
            background-color: #e8f5e8;
        }

        .file-preview-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }

        .file-preview {
            position: relative;
            display: inline-block;
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 10px;
            width: 200px;
        }

        .file-info {
            padding: 5px 8px;
            font-size: 12px;
            color: #666;
        }

        .file-remove {
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(255, 0, 0, 0.8);
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .file-remove:hover {
            background: rgba(255, 0, 0, 1);
        }

        .upload-hint {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }

        .file-input-button {
            background: #4CAF50;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 10px;
        }

        .file-input-button:hover {
            background: #45a049;
        }

        .submit-button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 20px;
            display: block;
            width: 200px;
            margin-left: auto;
            margin-right: auto;
        }

        .submit-button:hover {
            background: #45a049;
        }

        .submit-button:disabled {
            background: #cccccc;
            cursor: not-allowed;
        }

        .loading-indicator {
            display: none;
            margin-left: 10px;
        }

        .info-section {
            margin-top: 20px;
            padding: 15px;
            background: #f5f5f5;
            border-radius: 5px;
        }

        .info-section h3 {
            margin-top: 0;
        }

        .info-section ul {
            padding-left: 20px;
        }

        .alert {
            padding: 15px;
            margin-top: 20px;
            border-radius: 5px;
            display: none;
        }

        .alert-success {
            background-color: #dff0d8;
            color: #3c763d;
            border: 1px solid #d6e9c6;
        }

        .alert-danger {
            background-color: #f2dede;
            color: #a94442;
            border: 1px solid #ebccd1;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>明星生日需求上传</h1>
        
        <div class="section-title">Excel 文件上传</div>
        
        <!-- 文件上传区域 -->
        <div class="file-upload-container" id="file-upload-area">
            <div class="upload-hint">
                <p>📊 Excel文件上传区域</p>
                <p>支持拖拽文件到此处，或点击按钮选择文件</p>
                <p>仅支持格式：XLSX</p>
            </div>
            
            <input type="file" id="file-input" accept=".xlsx" style="display: none;">
            <button class="file-input-button" id="select-file-btn">
                选择Excel文件
            </button>
        </div>
        
        <!-- 文件预览区域 -->
        <div class="file-preview-container" id="file-preview-container">
            <!-- 上传的文件预览将在这里显示 -->
        </div>
        
        <!-- 提交按钮 -->
        <button id="submit-button" class="submit-button" disabled>
            提交文件
            <span class="spinner-border spinner-border-sm loading-indicator" id="loading-indicator" role="status" aria-hidden="true"></span>
        </button>
        
        <!-- 提示信息 -->
        <div id="alert-message" class="alert">
            <!-- 提示消息将在这里显示 -->
        </div>
        
    </div>

    <script>
        // 上传文件数组
        var uploadedFiles = [];
        // WebSocket实例 - 使用不同的变量名以避免冲突
        var wsConnection = null;
        // 是否通过服务器访问
        var isServerMode = false;

        // 页面加载完成后初始化
        
            // 初始化文件上传功能
            initFileUpload();
            
            // 检测是否为服务器模式
            if (window.location.protocol !== 'file:') {
                isServerMode = true;
                // 尝试连接WebSocket
                initWebSocketConnection();
            } else {
                console.log('本地文件模式，WebSocket功能不可用');
                showAlert('当前为本地文件模式，部分功能不可用', 'warning');
            }
            
            // 绑定按钮事件
            document.getElementById('select-file-btn').addEventListener('click', function() {
                document.getElementById('file-input').click();
            });
            
            document.getElementById('submit-button').addEventListener('click', submitFiles);
        

        // 连接WebSocket
        function initWebSocketConnection() {
            try {
                const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${wsProtocol}//${window.location.host}/ws/gpt/`;
                
                // 创建新的WebSocket连接
                wsConnection = new WebSocket(wsUrl);

                wsConnection.onopen = function() {
                    console.log('WebSocket连接成功');
                    showAlert('已连接到服务器', 'success');
                };

                wsConnection.onmessage = function(e) {
                    const data = JSON.parse(e.data);
                    console.log('收到消息:', data);
                    handleWebSocketMessage(data);
                };

                wsConnection.onclose = function() {
                    console.log('WebSocket连接已关闭');
                    showAlert('与服务器的连接已断开，请刷新页面重试', 'danger');
                };

                wsConnection.onerror = function(error) {
                    console.error('WebSocket错误:', error);
                    showAlert('连接错误，请刷新页面重试', 'danger');
                };
            } catch (error) {
                console.error('创建WebSocket连接时出错:', error);
                showAlert('无法建立与服务器的连接。部分功能可能无法使用。', 'danger');
            }
        }

        // 初始化文件上传功能
        function initFileUpload() {
            const uploadArea = document.getElementById('file-upload-area');
            const fileInput = document.getElementById('file-input');

            if (!uploadArea || !fileInput) {
                console.error('缺少必要的DOM元素');
                return;
            }

            // 设置文件输入监听器
            fileInput.addEventListener('change', function(e) {
                handleFiles(e.target.files);
            });

            // 拖拽上传功能
            setupDragAndDrop(uploadArea);
        }

        // 设置拖拽功能
        function setupDragAndDrop(uploadArea) {
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                e.stopPropagation();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                e.stopPropagation();
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                e.stopPropagation();
                uploadArea.classList.remove('dragover');
                handleFiles(e.dataTransfer.files);
            });
        }

        // 处理文件
        function handleFiles(files) {
            for (let file of files) {
                if (validateFile(file)) {
                    addFileToList(file);
                }
            }
        }

        // 验证文件
        function validateFile(file) {
            const allowedExtensions = ['.xlsx'];
            const fileName = file.name.toLowerCase();
            
            // 检查扩展名
            const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext));
            
            if (!hasValidExtension) {
                showAlert(`文件 ${file.name} 格式不支持。请选择 .xlsx 格式的 Excel 文件。`, 'danger');
                return false;
            }
            
            // 检查文件大小 (10MB限制)
            if (file.size > 10 * 1024 * 1024) {
                showAlert(`文件 ${file.name} 超过10MB限制。`, 'danger');
                return false;
            }
            
            return true;
        }

        // 添加文件到列表
        function addFileToList(file) {
            const fileId = 'file_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            
            // 读取文件内容
            const reader = new FileReader();
            reader.onload = function(e) {
                const fileData = {
                    id: fileId,
                    name: file.name,
                    size: file.size,
                    content: e.target.result // base64编码的文件内容
                };
                
                // 清空之前的文件（因为我们只允许一个文件）
                uploadedFiles = [];
                
                // 添加到文件数组
                uploadedFiles.push(fileData);
                
                // 更新文件列表显示
                updateFileList();
                
                // 启用提交按钮
                document.getElementById('submit-button').disabled = false;
            };
            
            reader.onerror = function() {
                showAlert(`文件 ${file.name} 读取失败`, 'danger');
            };
            
            reader.readAsDataURL(file);
        }

        // 更新文件列表显示
        function updateFileList() {
            const filePreviewContainer = document.getElementById('file-preview-container');
            
            // 清空列表
            filePreviewContainer.innerHTML = '';
            
            if (uploadedFiles.length > 0) {
                uploadedFiles.forEach(file => {
                    const filePreview = document.createElement('div');
                    filePreview.className = 'file-preview';
                    filePreview.id = `file_preview_${file.id}`;
                    
                    filePreview.innerHTML = `
                        <div class="file-icon">
                            <i style="font-size: 24px; color: #1D6F42;">📊</i>
                        </div>
                        <div class="file-info">
                            <div class="file-name">${file.name}</div>
                            <div class="file-size">${formatFileSize(file.size)}</div>
                        </div>
                        <button class="file-remove" onclick="removeFile('${file.id}')">×</button>
                    `;
                    
                    filePreviewContainer.appendChild(filePreview);
                });
            }
        }

        // 移除文件
        function removeFile(fileId) {
            uploadedFiles = uploadedFiles.filter(file => file.id !== fileId);
            updateFileList();
            
            // 如果没有文件，禁用提交按钮
            if (uploadedFiles.length === 0) {
                document.getElementById('submit-button').disabled = true;
            }
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 提交文件
        function submitFiles() {
            if (uploadedFiles.length === 0) {
                showAlert('请先上传文件', 'danger');
                return;
            }
            
            const submitButton = document.getElementById('submit-button');
            submitButton.disabled = true;
            document.getElementById('loading-indicator').style.display = 'inline-block';
            
            // 检查是否为服务器模式且WebSocket连接可用
            if (isServerMode && wsConnection && wsConnection.readyState === WebSocket.OPEN) {
                wsConnection.send(JSON.stringify({
                    type: 'demands_for_stars_birthday',
                    files: uploadedFiles
                }));
            } else {
                showAlert("WebSocket未连接，请确保通过服务器访问此页面", 'danger');
                submitButton.disabled = false;
                document.getElementById('loading-indicator').style.display = 'none';
            }
        }

        // 显示提示信息
        function showAlert(message, type) {
            const alertElement = document.getElementById('alert-message');
            alertElement.innerHTML = message;
            alertElement.className = `alert alert-${type}`;
            alertElement.style.display = 'block';
            
            // 5秒后自动隐藏
            setTimeout(() => {
                alertElement.style.display = 'none';
            }, 5000);
        }

        // 下载模板
        function downloadTemplate() {
            // 这里应该是模板下载的逻辑，可以是一个API调用或直接链接
            alert('模板下载功能尚未实现');
        }

        // 定义全局处理WebSocket消息的函数
        function handleWebSocketMessage(data) {
            if (data.type === 'demands_for_stars_birthday_response') {
                document.getElementById('loading-indicator').style.display = 'none';
                
                if (data.success) {
                    // 显示成功消息
                    showAlert(data.message, 'success');
                    
                    // 清空文件列表
                    uploadedFiles = [];
                    updateFileList();
                } else {
                    // 显示错误消息
                    showAlert(data.message, 'danger');
                    
                    // 重新启用提交按钮
                    document.getElementById('submit-button').disabled = false;
                }
            }
        }
    </script>
</body>
</html>
