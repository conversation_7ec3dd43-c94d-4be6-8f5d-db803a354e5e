{% load static %}
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>与图对话</title>
    <style>
        .container {
            max-width: 90%;
            margin-top: 15px;
            background: #fff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            margin-right: auto;
            margin-left: auto;
            position: relative;
        }

        .header-title {
            text-align: center;
            color: #2c3e50;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 30px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }

        .input-section {
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }

        .section-title {
            font-size: 18px;
            color: #2c3e50;
            margin-bottom: 15px;
            font-weight: 600;
        }



        /* 图片上传相关样式 */
        .image-upload-container {
            margin-top: 10px;
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            background-color: #f9f9f9;
            transition: all 0.3s ease;
        }

        .image-upload-container.dragover {
            border-color: #00E676;
            background-color: #e8f5e8;
        }

        .image-preview-container {
            margin-top: 15px;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .image-preview {
            position: relative;
            display: inline-block;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            background: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .image-preview img {
            max-width: 150px;
            max-height: 150px;
            display: block;
        }

        .image-info {
            padding: 8px;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #eee;
        }

        .image-remove {
            position: absolute;
            top: 5px;
            right: 5px;
            width: 20px;
            height: 20px;
            background: rgba(255, 0, 0, 0.7);
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .image-remove:hover {
            background: rgba(255, 0, 0, 1);
        }

        .upload-hint {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }

        .file-input-button {
            background: #4CAF50;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }

        .file-input-button:hover {
            background: #45a049;
        }



        .model-select {
            padding: 12px 15px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 14px;
            background: white;
            cursor: pointer;
            transition: border-color 0.3s ease;
        }

        .model-select:focus {
            outline: none;
            border-color: #3498db;
        }

        .submit-btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }

        .submit-btn:hover {
            background: linear-gradient(135deg, #2980b9, #1f5f8b);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }

        .submit-btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .loading-indicator {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .result-section {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #27ae60;
            display: none;
        }

        .result-content {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e1e8ed;
            white-space: pre-wrap;
            line-height: 1.6;
        }

        .copy-btn {
            background: #27ae60;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            font-size: 14px;
            cursor: pointer;
            margin-top: 10px;
            transition: background 0.3s ease;
        }

        .copy-btn:hover {
            background: #219a52;
        }

        .btn-xs {
            padding: 0.1rem 0.4rem;
            font-size: 0.75rem;
            line-height: 1.2;
            border-radius: 0.2rem;
        }

        /* 新增美化样式 */
        .feature-highlight {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }

        .feature-highlight h3 {
            margin: 0 0 10px 0;
            font-size: 20px;
        }

        .feature-highlight p {
            margin: 0;
            opacity: 0.9;
            font-size: 14px;
        }

        .stats-container {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .stat-item {
            text-align: center;
            flex: 1;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #3498db;
            display: block;
        }

        .stat-label {
            font-size: 12px;
            color: #7f8c8d;
            margin-top: 5px;
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: #ecf0f1;
            border-radius: 2px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            width: 0%;
            transition: width 0.3s ease;
        }

        .tip-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #f39c12;
        }

        .tip-box .tip-icon {
            color: #f39c12;
            margin-right: 8px;
        }

        .tip-box .tip-text {
            color: #856404;
            font-size: 14px;
            margin: 0;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 15px;
            }

            .stats-container {
                flex-direction: column;
                gap: 10px;
            }

            .header-title {
                font-size: 20px;
            }
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .input-section {
            animation: fadeInUp 0.6s ease-out;
        }

        .input-section:nth-child(2) { animation-delay: 0.1s; }
        .input-section:nth-child(3) { animation-delay: 0.2s; }
        .input-section:nth-child(4) { animation-delay: 0.3s; }

        /* 悬浮效果 */
        .input-section:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        /* 成功状态样式 */
        .success-message {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 12px 16px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #28a745;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header-title">
            📝 图片智能分析
        </div>

        <!-- 功能介绍 -->
        <div class="feature-highlight">
            <h3>🚀 智能图片分析助手</h3>
            <p>结合AI技术，深度分析上传的图片内容，为您提供专业的图片解读和分析结果</p>
        </div>

        <!-- 统计信息 -->
        <div class="stats-container">
            <div class="stat-item">
                <span class="stat-number" id="imageCount">0</span>
                <div class="stat-label">图片数量</div>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="analysisTime">0</span>
                <div class="stat-label">分析时间(秒)</div>
            </div>
        </div>

        <!-- 提示信息 -->
        <div class="tip-box">
            <span class="tip-icon">💡</span>
            <p class="tip-text">
                <strong>使用提示：</strong>
                上传图片并描述您希望从图片中获得的信息，AI将为您提供详细的图片分析结果。
            </p>
        </div>

        <!-- 模型选择 -->
        <div class="input-section">
            <div class="section-title">🤖 模型选择</div>
            <select id="modelSelect" class="model-select">
                {% for model in models %}
                <option value="{{ model.name }}">{{ model.name }}</option>
                {% endfor %}
            </select>
        </div>



        <!-- 图片上传区域 -->
        <div class="input-section">
            <div class="section-title">🖼️ 图片上传（可选）</div>

            <!-- 图片上传区域 -->
            <div class="image-upload-container" id="image-upload-area">
                <div class="upload-hint">
                    <p>📷 图片上传区域</p>
                    <p>支持拖拽图片到此处，或点击按钮选择文件</p>
                    <p>支持格式：JPG, PNG, GIF, WEBP</p>
                    <p id="paste-status" style="color: #666; font-size: 12px; margin-top: 5px;">
                        粘贴功能状态：<span id="paste-status-text">初始化中...</span>
                    </p>
                </div>

                <!-- 使用说明 -->
                <div style="margin-top: 10px; padding: 10px; border: 1px solid #ddd; border-radius: 8px; background: #f8f9fa; text-align: center;">
                    <p style="margin: 0; color: #666; font-size: 13px;">
                        💡 <strong>使用提示：</strong>复制图片后，点击绿色"从剪贴板粘贴图片"按钮即可快速上传
                    </p>
                </div>

                <!-- 图片分析需求输入框 -->
                <div style="margin-top: 15px; padding: 15px; border: 1px solid #ddd; border-radius: 8px; background: #fff;">
                    <label for="image-analysis-request" style="display: block; margin-bottom: 8px; font-weight: bold; color: #333; font-size: 14px;">
                        🤔 你想从图片里得到什么呢？
                    </label>
                    <textarea
                        id="image-analysis-request"
                        placeholder="例如：分析这个产品的特点、描述图片中的场景、提取图片中的文字、分析用户评论的情感等..."
                        style="width: 100%; min-height: 80px; padding: 10px; border: 1px solid #ccc; border-radius: 4px; font-size: 14px; resize: vertical; box-sizing: border-box;"
                    ></textarea>
                    <p style="margin: 8px 0 0 0; color: #666; font-size: 12px;">
                        💡 描述你希望AI从上传的图片中分析或提取的信息，这将帮助生成更精准的分析结果
                    </p>
                </div>

                <input type="file" id="file-input" accept="image/*" multiple style="display: none;">
                <button class="file-input-button" onclick="triggerFileSelect()">
                    选择图片文件
                </button>
                <button class="file-input-button" onclick="readClipboardImage()" style="margin-left: 10px; background: #28a745;">
                    从剪贴板粘贴图片
                </button>
                <p style="color: #f00; font-size: 12px; margin-top: 5px;">
                    如果点击按钮无效，请尝试直接在页面上按 Ctrl+V 粘贴图片。
                </p>
            </div>

            <!-- 图片预览区域 -->
            <div class="image-preview-container" id="image-preview-container">
                <!-- 上传的图片预览将在这里显示 -->
            </div>
        </div>

        <!-- 提交按钮 -->
        <button id="submitBtn" class="submit-btn">
            🚀 开始分析
        </button>

        <!-- 加载指示器 -->
        <div id="loadingIndicator" class="loading-indicator">
            <div class="spinner"></div>
            <div>正在分析中，请稍候...</div>
        </div>

        <!-- 结果显示区域 -->
        <div id="resultSection" class="result-section">
            <div class="section-title">📊 分析结果</div>
            <div id="resultContent" class="result-content"></div>
            <button id="copyBtn" class="copy-btn">📋 复制结果</button>
        </div>
    </div>

    <script src="{% static 'js/jquery.js' %}"></script>
    <script>
        // 全局变量
        let uploadedImages = [];
        let socket = null;
        let analysisStartTime = 0;

        // 页面加载完成后初始化
        // 使用立即执行函数和标志位，确保在动态加载时只执行一次
        (function() {
            if (window.isWordToImageQueryInitialized) {
                return;
            }
            
            initImageUpload();
            initWebSocket();
            initEventListeners();
            
            window.isWordToImageQueryInitialized = true;
        })();

        // 初始化WebSocket连接
        function initWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/gpt/`;
            
            socket = new WebSocket(wsUrl);
            
            socket.onopen = function(event) {
                console.log('WebSocket连接已建立');
            };
            
            socket.onmessage = function(event) {
                const data = JSON.parse(event.data);
                handleWebSocketMessage(data);
            };
            
            socket.onclose = function(event) {
                console.log('WebSocket连接已关闭');
            };
            
            socket.onerror = function(error) {
                console.error('WebSocket错误:', error);
            };
        }

        // 处理WebSocket消息
        function handleWebSocketMessage(data) {
            if (data.target === 7) { // 新的target值用于文字转图片查询
                if (data.message) {
                    const resultContent = document.getElementById('resultContent');
                    resultContent.innerHTML += data.message;
                }

                if (data.status === 1) {
                    // 分析完成
                    document.getElementById('loadingIndicator').style.display = 'none';
                    document.getElementById('submitBtn').disabled = false;
                    document.getElementById('resultSection').style.display = 'block';

                    // 停止计时
                    analysisStartTime = 0;

                    // 显示成功消息
                    showSuccessMessage('分析完成！');
                } else if (data.status === 0) {
                    // 分析失败
                    document.getElementById('loadingIndicator').style.display = 'none';
                    document.getElementById('submitBtn').disabled = false;
                    analysisStartTime = 0;
                    alert('分析失败，请重试');
                }
            }
        }

        // 显示成功消息
        function showSuccessMessage(message) {
            const successDiv = document.createElement('div');
            successDiv.className = 'success-message';
            successDiv.innerHTML = `✅ ${message}`;

            const container = document.querySelector('.container');
            container.insertBefore(successDiv, document.getElementById('resultSection'));

            // 3秒后自动移除
            setTimeout(() => {
                if (successDiv.parentNode) {
                    successDiv.parentNode.removeChild(successDiv);
                }
            }, 3000);
        }



        // 初始化事件监听器
        function initEventListeners() {
            const submitBtn = document.getElementById('submitBtn');
            const copyBtn = document.getElementById('copyBtn');

            // 提交按钮事件
            submitBtn.addEventListener('click', submitAnalysisRequest);

            // 复制按钮事件
            copyBtn.addEventListener('click', copyResult);
        }

        // 更新文字统计
        function updateTextStats() {
            // 由于移除了文字输入框，这里设置为0
            document.getElementById('textLength').textContent = '0';
        }

        // 更新图片统计
        function updateImageStats() {
            document.getElementById('imageCount').textContent = uploadedImages.length;
        }

        // 更新分析时间
        function updateAnalysisTime() {
            if (analysisStartTime > 0) {
                const currentTime = Math.floor((Date.now() - analysisStartTime) / 1000);
                document.getElementById('analysisTime').textContent = currentTime;
            }
        }

        // 图片上传功能初始化
        function initImageUpload() {
            console.log('初始化图片上传功能...');
            const uploadArea = document.getElementById('image-upload-area');
            const fileInput = document.getElementById('file-input');
            const previewContainer = document.getElementById('image-preview-container');

            if (!uploadArea || !fileInput || !previewContainer) {
                console.error('图片上传相关元素未找到');
                return;
            }

            // 设置文件选择事件监听器
            autoSetupEventListeners(fileInput);

            // 设置拖拽功能
            setupDragAndDrop(uploadArea);

            // 自动设置粘贴功能
            autoSetupPasteFunction();

            console.log('图片上传功能初始化完成');
        }

        // 自动设置事件监听器
        function autoSetupEventListeners(fileInput) {
            console.log('自动设置文件选择事件监听器...');
            fileInput.addEventListener('change', function(event) {
                console.log('文件选择事件触发');
                const files = Array.from(event.target.files);
                handleFiles(files);
            });
        }

        // 设置拖拽功能
        function setupDragAndDrop(uploadArea) {
            console.log('设置拖拽功能...');

            uploadArea.addEventListener('dragover', function(event) {
                event.preventDefault();
                event.stopPropagation();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', function(event) {
                event.preventDefault();
                event.stopPropagation();
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', function(event) {
                event.preventDefault();
                event.stopPropagation();
                uploadArea.classList.remove('dragover');

                const files = Array.from(event.dataTransfer.files);
                console.log('拖拽文件数量:', files.length);
                handleFiles(files);
            });
        }

        // 触发文件选择
        window.triggerFileSelect = function() { // 暴露到全局作用域
            console.log('触发文件选择...');
            document.getElementById('file-input').click();
        };

        // 处理文件
        function handleFiles(files) {
            console.log('处理文件，数量:', files.length);

            const imageFiles = files.filter(file => {
                const isImage = file.type.startsWith('image/');
                console.log('文件:', file.name, '是图片:', isImage, '类型:', file.type);
                return isImage;
            });

            if (imageFiles.length === 0) {
                alert('请选择图片文件！');
                return;
            }

            console.log('有效图片文件数量:', imageFiles.length);

            imageFiles.forEach(file => {
                console.log('处理图片文件:', file.name, '大小:', file.size);

                if (file.size > 50 * 1024 * 1024) { // 50MB限制
                    alert(`文件 ${file.name} 超过50MB限制，已跳过`);
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    console.log('文件读取完成:', file.name);
                    addImagePreview(file, e.target.result);
                };
                reader.onerror = function() {
                    console.error('文件读取失败:', file.name);
                    alert(`文件 ${file.name} 读取失败`);
                };
                reader.readAsDataURL(file);
            });
        }

        // 添加图片预览
        function addImagePreview(file, dataUrl) {
            console.log('添加图片预览:', file.name, '大小:', file.size);
            const previewContainer = document.getElementById('image-preview-container');
            const imageId = 'img_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

            // 添加到上传图片数组
            uploadedImages.push({
                id: imageId,
                file: file,
                dataUrl: dataUrl,
                name: file.name,
                size: file.size
            });

            console.log('当前上传图片数量:', uploadedImages.length);

            // 创建预览元素
            const previewDiv = document.createElement('div');
            previewDiv.className = 'image-preview';
            previewDiv.id = imageId;

            previewDiv.innerHTML = `
                <img src="${dataUrl}" alt="${file.name}">
                <div class="image-info">
                    <div>${file.name}</div>
                    <div>${formatFileSize(file.size)}</div>
                </div>
                <button class="image-remove" onclick="removeImage('${imageId}')" title="删除图片">×</button>
            `;

            previewContainer.appendChild(previewDiv);
            updateImageStats();
        }

        // 移除图片
        function removeImage(imageId) {
            // 从数组中移除
            uploadedImages = uploadedImages.filter(img => img.id !== imageId);

            // 从DOM中移除
            const element = document.getElementById(imageId);
            if (element) {
                element.remove();
            }

            console.log('移除图片后，当前图片数量:', uploadedImages.length);
            updateImageStats();
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 获取上传的图片数据（用于发送到后端）
        function getUploadedImagesData() {
            console.log('获取图片数据，当前上传的图片数量:', uploadedImages.length);
            return uploadedImages.map(img => ({
                id: img.id,
                name: img.name,
                size: img.size,
                dataUrl: img.dataUrl
            }));
        }

        // 自动设置粘贴功能
        function autoSetupPasteFunction() {
            console.log('设置粘贴功能...');

            // 更新粘贴状态
            const pasteStatusText = document.getElementById('paste-status-text');
            if (pasteStatusText) {
                pasteStatusText.textContent = '已启用';
                pasteStatusText.style.color = '#28a745';
            }

            // 全局粘贴事件监听器
            document.body.addEventListener('paste', pasteHandler, false);
        }

        // 粘贴处理函数
        function pasteHandler(event) {
            console.log('粘贴事件触发');
            const items = event.clipboardData.items;

            for (let i = 0; i < items.length; i++) {
                const item = items[i];
                console.log('粘贴项类型:', item.type);

                if (item.type.indexOf('image') !== -1) {
                    console.log('检测到图片粘贴');
                    const file = item.getAsFile();
                    if (file) {
                        console.log('粘贴的图片文件:', file.name || '未命名', '大小:', file.size);
                        handleFiles([file]);
                        event.preventDefault();
                        return;
                    }
                }
            }
        }

        // 从剪贴板读取图片 (由按钮点击触发)
        window.readClipboardImage = function() { // 暴露到全局作用域
            console.log('readClipboardImage 函数被调用...'); // Added for debugging
            console.log('尝试从剪贴板读取图片...');

            if (navigator.clipboard && navigator.clipboard.read) {
                navigator.clipboard.read().then(items => {
                    console.log('剪贴板项目数量:', items.length);

                    let imageFound = false;
                    for (const item of items) {
                        console.log('剪贴板项目类型:', item.types);

                        for (const type of item.types) {
                            if (type.startsWith('image/')) {
                                console.log('找到图片类型:', type);
                                imageFound = true;
                                item.getType(type).then(blob => {
                                    console.log('获取到图片blob:', blob.size, 'bytes');
                                    const file = new File([blob], `clipboard-image-${Date.now()}.png`, { type: blob.type });
                                    handleFiles([file]);
                                }).catch(err => {
                                    console.error('获取剪贴板图片失败:', err);
                                    alert('获取剪贴板图片失败，可能是权限问题或浏览器限制。请尝试直接在页面上按 Ctrl+V 粘贴图片。');
                                });
                                return; // 只处理第一个图片
                            }
                        }
                    }

                    if (!imageFound) {
                        console.log('剪贴板中没有找到图片');
                        alert('剪贴板中没有图片。请确保您已复制图片（例如，从文件管理器复制图片文件，或在网页上右键点击图片选择复制）。');
                    }
                }).catch(err => {
                    console.error('读取剪贴板失败 (权限或安全问题):', err);
                    alert('浏览器安全设置阻止了直接读取剪贴板。请尝试直接在页面上按 Ctrl+V 粘贴图片，或点击"选择图片文件"按钮上传。');
                });
            } else {
                console.log('当前浏览器不支持 Clipboard API 的 read() 方法。');
                alert('当前浏览器不支持直接点击按钮读取剪贴板图片。请尝试直接在页面上按 Ctrl+V 粘贴图片，或点击"选择图片文件"按钮上传。');
            }
        }

        // 提交分析请求
        function submitAnalysisRequest() {
            const imageAnalysisRequest = document.getElementById('image-analysis-request').value.trim();
            const model = document.getElementById('modelSelect').value;

            if (uploadedImages.length === 0) {
                alert('请上传图片');
                return;
            }

            if (!socket || socket.readyState !== WebSocket.OPEN) {
                alert('连接失败，请刷新页面重试');
                return;
            }

            // 禁用提交按钮并显示加载指示器
            document.getElementById('submitBtn').disabled = true;
            document.getElementById('loadingIndicator').style.display = 'block';
            document.getElementById('resultSection').style.display = 'none';
            document.getElementById('resultContent').innerHTML = '';

            // 开始计时
            analysisStartTime = Date.now();
            document.getElementById('analysisTime').textContent = '0';

            // 启动计时器
            const timer = setInterval(() => {
                updateAnalysisTime();
            }, 1000);

            // 构建请求数据
            const requestData = {
                source: 7, // 新的source值用于文字转图片查询
                model: model,
                textContent: '', // 不再使用文字输入
                uploadedImages: getUploadedImagesData(),
                imageAnalysisRequest: imageAnalysisRequest || '分析图片内容'
            };

            // 发送请求
            socket.send(JSON.stringify(requestData));
        }

        // 复制结果
        function copyResult() {
            const resultContent = document.getElementById('resultContent');
            const textToCopy = resultContent.textContent || resultContent.innerText;
            
            navigator.clipboard.writeText(textToCopy).then(function() {
                alert('结果已复制到剪贴板');
            }).catch(function(error) {
                console.error('复制失败:', error);
                alert('复制失败，请手动选择复制');
            });
        }
    </script>
</body>

</html>
