from IPython.utils import io
from serpapi import GoogleSearch
import os
import string
import re


import openai
import urllib.request, json

from langchain_openai import ChatOpenAI
from serpapi import GoogleSearch
from dotenv import load_dotenv
from IPython.utils import io
# from Multimodal_gemini import ImageAnalyzer



load_dotenv(r'D:\硕士\aiagent\gpt_CWSC\.env',override=True) # 关键：`override=True`

openai_api_key = os.getenv("OPENAI_API_KEY")
deepseek_api_key = os.getenv("DEEPSEEK_API_KEY_2")
print(deepseek_api_key)
print(openai.api_key)


template = """你是Wilson品牌的小红书文案编辑专家，请按照以下模式并参考示例(重要)进行文案创作：
# 小红书文案编辑专家的任务
## 根据提供的信息：产品名称是{Product_Information}、产品的受众人群是{Product_Audience}、产品的核心卖点(也是最重要的一点)是{Product_Selling_Points}以及文案主题是{Copy_Theme}，充分理解并分析品牌的调性、产品的特点和卖点，为{Product_Information}生成一条标题和相应的正文文案。
#产品补充背景
{Additional_Resource}
## 示例
### 示例1：
- 标题：红土之彩🌈入胜如她
- 正文：
以虹为翼，每一步👟都出「彩」
Wilson Intrigue女子红土网球鞋
急停蹬转，滑步致胜🏆
红土飞扬，烙印精彩征程
#Wilson #Wilson威尔胜 #网球
#LiveLikeAnAthlete #为生活而动
#Wilson网球 #Wilson网球鞋
#网球鞋 #网球暴击美学
#网球热土 #红土之彩#入胜如她
#法网 #法国网球公开赛 #法网2025
### 示例2：
- 标题：每次律动，都灵动飘逸👗
- 正文：
轻装律动，激活身心💛💚
解锁🔓浅春新色，勇敢奔赴热爱
氧气感黄绿，点缀精致细节✨
贴合身形，勾勒流畅曲线
裙摆飞扬，盛放春之踪迹
优雅与飒爽，不止场上
#Wilson #Wilson威尔胜 #网球
#LiveLikeAnAthlete #为生活而动
#Wilson网球 #Wilson网球裙
#蜜糖裙 #Wilson蜜糖裙
#Wilson网球穿搭 #春日网球穿搭
#网球fitcheck#网球暴击美学
#网球热土 #心动浅春系 #多元运动风
#下一站网球天堂 #球场落日收藏家
#今年的追求是网球#动一动过春天
#示例3：
- 标题：肆意挥汗🔝，清爽登场🔋
- 正文：
上场挥拍，纵横红土
滑步救球🎾，应对自如
梧桐青经典POLO+梭织短裤
舒适加持专业，透气干爽
助力肆意挥汗💦，掌控局势
畅意奔赴红土，全力挥拍竞逐
#Wilson#Wilson威尔胜#网球
#LiveLikeAnAthlete#为生活而动
#Wilson网球#Wilson网球拍#网球拍
#Wilson网球装备#Wilson网球穿搭
#Wilson网球鞋#网球暴击美学
#网球热土#红土之彩#网球真的红了
#聊球俱乐部#法网聊天室
#法网#法国网球公开赛#法网2025
# 文案生成流程  
1. 理解品牌与产品：分析品牌调性，明确目标受众需求与使用场景。  
2. 提炼卖点：提取产品独特卖点，以符合品牌调性的方式展示时尚与专业性。  
3. 标题创作：以简洁有力的短句或互动性词句呈现，吸引注意。标题使用趣味性或情感化的表达，营造氛围感，增强吸引力。  
4. 正文撰写：用短句或列点直击要点，一秒读懂核心功能与专业亮点；灵活插入⚡️🎾🔥等表情符号，兼顾高质感与亲和力。
5. 校对优化：确保文案符合品牌调性和格式要求，调整以增强吸引力和可读性。
"""









llm_gpt4o = ChatOpenAI(model="gpt-4o", temperature=1.0,api_key = openai_api_key,base_url='https://xiaoai.plus/v1',top_p=0.7)
llm_deepseek = ChatOpenAI(model="GPT-4.1", temperature=0,base_url='https://ai.hao666666.online/openai')
# llm_deepseek = ChatOpenAI(
#     model='deepseek-chat',
#     openai_api_key=deepseek_api_key,
#     openai_api_base='https://api.deepseek.com/v1',
#     max_tokens=100,
#     temperature=1.5
# )
from langchain_core.prompts import ChatPromptTemplate

prompt = ChatPromptTemplate.from_template(template)
# 定义字典
prompt_dict = {
    "Product_Information": "Rush Pro 4.5男子红土网球鞋",
    "Product_Audience": "网球爱好者",
    "Product_Selling_Points": "专业红土网球鞋，提供卓越的抓地力和舒适性",
    "Copy_Theme": "运动、网球",

}

# analyzer = ImageAnalyzer(
#         env_path=r'D:\硕士\aiagent\gpt_CWSC\.env',
#         model_name="gemini-2.0-flash",
#         temperature=0,
#         timeout=30
#     )
#
# print("\n" + "=" * 50)


# prompt1 = "使用中文描述这些图片的{}产品，字数大概300左右".format(prompt_dict['Product_Information'])
#
# print("\n=== 示例4: 分析多张本地图片（简化列表） ===")
# local_images = [
#                 r"D:\硕士\aiagent\gpt_CWSC\pictures\1.png",
#                 r"D:\硕士\aiagent\gpt_CWSC\pictures\2.png"  # 假设存在
#             ]
#
# result = analyzer.analyze_image(local_images, prompt=prompt1,is_local= True)
# if result["success"]:
#     print("分析结果:", result["result"])
#     print(f"处理图片数: {result['processed_images_count']}/{result['total_images_count']}")
# else:
#     print("分析失败:", result["error"])






#additional_data2 =  result["result"]
additional_data2 = """这款Wilson Rush Pro 4.5男子红土网球鞋，专为红土场地设计，在性能和外观上都展现了专业水准。

鞋面以白色为主色调，简洁大气，材质透气性良好，保证运动时的舒适度。鞋身侧面醒目的深蓝色Wilson标志，搭配红黄黑三色条纹点缀，更显时尚感和品牌辨识度。深蓝色鞋带与鞋领内衬相呼应，细节之处体现精致。

鞋底采用红土专用纹路设计，提供卓越的抓地力和耐磨性，帮助球员在红土场地上灵活移动，快速启动和制动。中底采用缓震技术，有效吸收冲击力，减轻运动疲劳，保护关节。

从图片中可以看出，这款网球鞋的整体设计注重功能性和舒适性，同时兼顾了时尚外观，是红土场地网球爱好者的理想选择。无论是专业运动员还是业余爱好者，都能感受到这款鞋带来的出色性能和舒适体验。"""
print(additional_data2)
prompt_dict['Additional_Resource'] = additional_data2

# 使用字典填充


text1 = """
角色：一名产品受众人群分析师。

指令：根据补充资料细化人类给出的的受众人群，字数大概在20字左右,只需返回最重要的结果即可，优化后的结果以双引号输出。

补充资料：{additional_data}

"""
#
Product_Audience_prompt = ChatPromptTemplate(
    [
        (
            "system",
            text1
        ),
        ("human", "{input}"),
    ]
)
prompt_dict['Product_Audience'] =llm_deepseek.invoke(Product_Audience_prompt.format(additional_data = additional_data2,input=prompt_dict['Product_Audience'])).content

prompt = prompt.format(**prompt_dict)
print(prompt)
res = llm_gpt4o.invoke(prompt)
print(res.content)


